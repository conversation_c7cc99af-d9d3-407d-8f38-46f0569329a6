2025-08-05 17:42:03.569 | INFO     | src.utils.log_config:_log_initialization_info:227 | 日志系统初始化完成
2025-08-05 17:42:03.569 | INFO     | src.utils.log_config:_log_initialization_info:228 | 日志级别: INFO
2025-08-05 17:42:03.569 | INFO     | src.utils.log_config:_log_initialization_info:229 | 控制台输出: True
2025-08-05 17:42:03.569 | INFO     | src.utils.log_config:_log_initialization_info:230 | 文件输出: True
2025-08-05 17:42:03.569 | INFO     | src.utils.log_config:_log_initialization_info:236 | 日志文件路径: logs/salary_system.log
2025-08-05 17:42:03.569 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-05 17:42:06.350 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-05 17:42:06.350 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-05 17:42:06.365 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-05 17:42:06.365 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-05 17:42:06.365 | INFO     | __main__:setup_qt_exception_handling:218 | 🔧 [P0-修复] PyQt专用异常处理机制已启用
2025-08-05 17:42:06.365 | INFO     | __main__:setup_app_logging:349 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-05 17:42:06.365 | INFO     | __main__:main:413 | 初始化核心管理器...
2025-08-05 17:42:06.365 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-08-05 17:42:06.365 | INFO     | src.modules.system_config.config_manager:load_config:340 | 正在加载配置文件: config.json
2025-08-05 17:42:06.365 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-08-05 17:42:06.365 | INFO     | src.modules.system_config.config_manager:load_config:352 | 配置文件加载成功
2025-08-05 17:42:06.365 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-08-05 17:42:06.397 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-08-05 17:42:06.397 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-05 17:42:06.397 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-08-05 17:42:06.397 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-08-05 17:42:06.397 | INFO     | __main__:main:418 | 核心管理器初始化完成。
2025-08-05 17:42:06.397 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-05 17:42:06.397 | INFO     | src.core.table_sort_state_manager:__init__:174 | 表级排序状态管理器初始化完成
2025-08-05 17:42:06.397 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-08-05 17:42:06.397 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-05 17:42:06.397 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-05 17:42:06.397 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-05 17:42:06.397 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-05 17:42:06.412 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:10349 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-05 17:42:06.412 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-08-05 17:42:06.412 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:10204 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-05 17:42:06.412 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:10242 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-05 17:42:06.490 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-05 17:42:06.490 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-05 17:42:06.490 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-08-05 17:42:06.605 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:139 | 🔧 [配置修复] 创建了完整的默认配置，包含基本字段映射和模板
2025-08-05 17:42:06.605 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-05 17:42:06.605 | INFO     | src.core.unified_state_manager:_load_state:554 | 状态文件不存在，使用默认状态
2025-08-05 17:42:06.605 | INFO     | src.core.unified_state_manager:__init__:181 | 统一状态管理器初始化完成
2025-08-05 17:42:06.605 | INFO     | src.core.unified_data_request_manager:__init__:190 | 统一数据请求管理器初始化完成
2025-08-05 17:42:06.605 | INFO     | src.core.unified_data_request_manager:__init__:190 | 统一数据请求管理器初始化完成
2025-08-05 17:42:06.605 | INFO     | src.core.unified_state_manager:_load_state:554 | 状态文件不存在，使用默认状态
2025-08-05 17:42:06.605 | INFO     | src.core.unified_state_manager:__init__:181 | 统一状态管理器初始化完成
2025-08-05 17:42:06.715 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-08-05 17:42:06.715 | INFO     | src.core.unified_state_manager:_load_state:554 | 状态文件不存在，使用默认状态
2025-08-05 17:42:06.715 | INFO     | src.core.unified_state_manager:__init__:181 | 统一状态管理器初始化完成
2025-08-05 17:42:06.731 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-05 17:42:06.731 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-05 17:42:06.731 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:996 | 🎯 [事件驱动] 事件监听器设置完成
2025-08-05 17:42:06.731 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:1011 | 🎯 [统一状态管理] 状态同步完成
2025-08-05 17:42:06.731 | INFO     | src.modules.format_management.format_config:load_config:385 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-08-05 17:42:06.731 | INFO     | src.modules.format_management.format_config:save_config:435 | 🔧 [格式配置] 配置文件保存成功
2025-08-05 17:42:06.731 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:736 | 🔧 [自动修复] 为表 salary_data_2025_07_active_employees 自动生成字段类型配置
2025-08-05 17:42:06.731 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:920 | 🔧 [智能匹配] 表 salary_data_2025_07_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-05 17:42:06.731 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:754 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_07_active_employees
2025-08-05 17:42:06.731 | WARNING  | src.modules.format_management.field_registry:_validate_field_type_consistency:1908 | 🔧 [P3增强] 字段类型一致性验证失败: 'fixes'
2025-08-05 17:42:06.731 | WARNING  | src.modules.format_management.field_registry:_validate_existing_display_fields:1942 | 🔧 [P3增强] existing_display_fields验证失败: 'fixes'
2025-08-05 17:42:06.731 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:777 | 🔧 [架构优化] 配置一致性警告: ['🔧 [P1-CRITICAL修复] 表 salary_data_2025_07_active_employees existing_display_fields为空且无法自动生成，将导致FormatRenderer降级处理']
2025-08-05 17:42:06.731 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2071 | 🔧 [P1-1修复] 表 active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-05 17:42:06.747 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2071 | 🔧 [P1-1修复] 表 retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-05 17:42:06.747 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2071 | 🔧 [P1-1修复] 表 pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-05 17:42:06.747 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2071 | 🔧 [P1-1修复] 表 part_time_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'hourly_rate', 'hours_worked']...
2025-08-05 17:42:06.747 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2071 | 🔧 [P1-1修复] 表 contract_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'base_salary', 'performance_bonus']...
2025-08-05 17:42:06.747 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2071 | 🔧 [P1-1修复] 表 a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-05 17:42:06.762 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2081 | 🔧 [架构优化] 应用智能修复策略，执行 6 项修复操作
2025-08-05 17:42:06.762 | INFO     | src.modules.format_management.field_registry:save_mappings:661 | 🏷️ [字段注册] 字段映射保存成功
2025-08-05 17:42:06.762 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:785 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-05 17:42:06.762 | INFO     | src.modules.format_management.field_registry:load_mappings:628 | 🏷️ [字段注册] 字段映射加载成功
2025-08-05 17:42:06.762 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:148 | 🎯 [统一格式管理] 系统初始化完成
2025-08-05 17:42:06.762 | INFO     | src.modules.format_management.unified_format_manager:__init__:132 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-08-05 17:42:06.762 | INFO     | src.modules.format_management.unified_format_manager:__init__:1194 | 🔧 [单例优化] 单例统一格式管理器初始化完成
2025-08-05 17:42:06.762 | INFO     | src.services.table_data_service:__init__:90 | 表格数据服务初始化完成
2025-08-05 17:42:06.762 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 271.9ms
2025-08-05 17:42:06.793 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-05 17:42:06.793 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-05 17:42:06.793 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-05 17:42:06.793 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-05 17:42:06.793 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-05 17:42:06.793 | INFO     | src.gui.prototype.prototype_main_window:__init__:3248 | 🚀 性能管理器已集成
2025-08-05 17:42:06.793 | INFO     | src.gui.prototype.prototype_main_window:__init__:3250 | ✅ 新架构集成成功！
2025-08-05 17:42:06.793 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3362 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-05 17:42:06.793 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3328 | ✅ 新架构事件监听器设置完成
2025-08-05 17:42:06.793 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-05 17:42:06.793 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-05 17:42:06.793 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-05 17:42:07.105 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2461 | 菜单栏创建完成
2025-08-05 17:42:07.105 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-05 17:42:07.105 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-05 17:42:07.105 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-05 17:42:07.105 | INFO     | src.gui.prototype.prototype_main_window:__init__:2437 | 菜单栏管理器初始化完成
2025-08-05 17:42:07.121 | INFO     | src.gui.table_header_manager:__init__:84 | 🔧 [P0-修复] 线程安全清理定时器已启动
2025-08-05 17:42:07.121 | INFO     | src.gui.table_header_manager:__init__:108 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-05 17:42:07.121 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5000 | 管理器设置完成，包含增强版表头管理器
2025-08-05 17:42:07.121 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5005 | 🔧 开始应用窗口级Material Design样式...
2025-08-05 17:42:07.121 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-05 17:42:07.121 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-05 17:42:07.121 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5012 | ✅ 窗口级样式应用成功
2025-08-05 17:42:07.121 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5053 | ✅ 响应式样式监听设置完成
2025-08-05 17:42:07.121 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-05 17:42:07.121 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-08-05 17:42:07.136 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1299 | 开始从元数据动态加载工资数据...
2025-08-05 17:42:07.136 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1306 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-08-05 17:42:07.154 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:753 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-05 17:42:07.155 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:786 | 恢复导航状态: 0个展开项
2025-08-05 17:42:07.156 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-08-05 17:42:07.157 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:530 | 增强导航面板初始化完成
2025-08-05 17:42:07.195 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-05 17:42:07.196 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2172 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-05 17:42:07.224 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1352 | 快捷键注册完成: 18/18 个
2025-08-05 17:42:07.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1838 | 拖拽排序管理器初始化完成
2025-08-05 17:42:07.253 | INFO     | src.modules.data_management.data_flow_validator:__init__:78 | 🔧 [数据验证器] 初始化完成，验证级别: moderate
2025-08-05 17:42:07.254 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-05 17:42:07.257 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-05 17:42:07.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2207 | 🔧 [排序修复] 数据流验证器和状态管理器初始化成功
2025-08-05 17:42:07.260 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-05 17:42:07.263 | INFO     | src.core.unified_state_manager:_load_state:579 | 状态已从文件加载: state/unified_state.json
2025-08-05 17:42:07.264 | INFO     | src.core.unified_state_manager:__init__:181 | 统一状态管理器初始化完成
2025-08-05 17:42:07.265 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2259 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-05 17:42:07.283 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:337 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-05 17:42:07.284 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:99 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-05 17:42:07.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2306 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-05 17:42:07.286 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1544 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-05 17:42:07.287 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1545 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-05 17:42:07.287 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1547 | 🔧 [列宽保存修复] 配置文件存在: False
2025-08-05 17:42:07.289 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1548 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-05 17:42:07.290 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1549 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-05 17:42:07.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2313 | 列宽管理器初始化完成
2025-08-05 17:42:07.299 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2327 | 🔧 [P0-CRITICAL修复] 统一格式管理器初始化成功
2025-08-05 17:42:07.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2451 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-05 17:42:07.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2349 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-05 17:42:07.303 | WARNING  | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:2099 | 无法获取主窗口引用，使用备用方案显示空表格
2025-08-05 17:42:07.304 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4601 | 🔧 [P0-CRITICAL修复] 数据或表头为空，跳过一致性验证
2025-08-05 17:42:07.305 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:set_data:3093 | 设置表格数据失败: list index out of range
2025-08-05 17:42:07.306 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8072 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-05 17:42:07.307 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8085 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-05 17:42:07.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1741 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-05 17:42:07.314 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:3141 | 🔧 [P2-修复] 数据设置后已恢复列宽: default_table
2025-08-05 17:42:07.315 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2121 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个
2025-08-05 17:42:07.324 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:296 | 分页组件Material Design样式应用成功
2025-08-05 17:42:07.337 | INFO     | src.gui.widgets.pagination_widget:__init__:174 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-05 17:42:07.339 | INFO     | src.gui.widgets.pagination_widget:__init__:182 | 分页组件初始化完成
2025-08-05 17:42:07.393 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:524 | 控制面板按钮信号连接完成
2025-08-05 17:42:07.435 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-08-05 17:42:07.435 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-05 17:42:07.439 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:4970 | 快捷键设置完成
2025-08-05 17:42:07.439 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:4959 | 主窗口UI设置完成。
2025-08-05 17:42:07.440 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5156 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-05 17:42:07.442 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5188 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-05 17:42:07.443 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5197 | ✅ 已连接分页组件事件到新架构
2025-08-05 17:42:07.445 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5199 | 信号连接设置完成
2025-08-05 17:42:07.452 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6188 | 已加载字段映射信息，共6个表的映射
2025-08-05 17:42:07.460 | WARNING  | src.gui.prototype.prototype_main_window:set_data:709 | 尝试设置空数据，将显示提示信息。
2025-08-05 17:42:07.461 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:7456 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-05 17:42:07.467 | WARNING  | src.gui.prototype.prototype_main_window:set_data:729 | 尝试设置空数据，将显示提示信息。
2025-08-05 17:42:07.468 | INFO     | src.gui.widgets.pagination_widget:reset:541 | 分页状态已重置
2025-08-05 17:42:07.470 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:7471 | 已显示标准空表格，表头数量: 22
2025-08-05 17:42:07.472 | INFO     | src.gui.widgets.pagination_widget:reset:541 | 分页状态已重置
2025-08-05 17:42:07.473 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:7456 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-05 17:42:07.478 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:7471 | 已显示标准空表格，表头数量: 22
2025-08-05 17:42:07.483 | INFO     | src.gui.prototype.prototype_main_window:__init__:3302 | 原型主窗口初始化完成
2025-08-05 17:42:07.722 | INFO     | __main__:main:440 | 应用程序启动成功
2025-08-05 17:42:07.730 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8107 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-05 17:42:07.731 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8076 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-05 17:42:07.732 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8090 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet', 'parent_opacity_effect']
2025-08-05 17:42:07.733 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:8505 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet', 'parent_opacity_effect']
2025-08-05 17:42:07.827 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-05 17:42:07.827 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1939 | MainWorkspaceArea 响应式适配: sm
2025-08-05 17:42:07.834 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8076 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-05 17:42:07.883 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8096 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-05 17:42:07.927 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8076 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-05 17:42:07.952 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1321 | 执行延迟的工资数据加载...
2025-08-05 17:42:07.952 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1602 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-08-05 17:42:07.953 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1610 | 找到工资表节点: 💰 工资表
2025-08-05 17:42:07.960 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-05 17:42:07.964 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:976 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-05 17:42:07.965 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:888 | 找到 3 个总表
2025-08-05 17:42:07.966 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1662 | 检测到数据库中没有工资数据表，直接使用兜底数据
2025-08-05 17:42:07.967 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1444 | 使用兜底数据加载导航
2025-08-05 17:42:07.967 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1197 | 执行延迟的自动选择最新数据...
2025-08-05 17:42:07.968 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1137 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-05 17:42:07.969 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-08-05 17:42:07.970 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-05 17:42:07.971 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-08-05 17:42:07.972 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1272 | 🔧 [P0-CRITICAL修复] 检查表状态失败，2.0s后重试...
2025-08-05 17:42:09.978 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-08-05 17:42:09.979 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-05 17:42:09.981 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-08-05 17:42:09.982 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1272 | 🔧 [P0-CRITICAL修复] 检查表状态失败，2.0s后重试...
2025-08-05 17:42:11.984 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-08-05 17:42:11.985 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-05 17:42:11.988 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-08-05 17:42:11.989 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1272 | 🔧 [P0-CRITICAL修复] 检查表状态失败，2.0s后重试...
2025-08-05 17:42:13.991 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-08-05 17:42:13.992 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-05 17:42:13.995 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-08-05 17:42:13.996 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1282 | 🔧 [P1-2修复] 4次尝试均失败，可能数据导入尚未完成
2025-08-05 17:42:13.997 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1142 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-05 17:42:13.997 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1217 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-05 17:42:14.000 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1350 | 导航树刷新完成，重新执行自动选择...
2025-08-05 17:42:14.000 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-08-05 17:42:14.000 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-05 17:42:14.000 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-08-05 17:42:14.000 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1355 | 未找到最新工资数据路径
2025-08-05 17:42:14.016 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:584 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-05 17:42:14.016 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5430 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-05 17:42:14.016 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-05 17:42:14.016 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:72 | 多Sheet导入器初始化完成
2025-08-05 17:42:14.016 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-08-05 17:42:14.032 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-05 17:42:14.032 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-08-05 17:42:14.078 | INFO     | src.gui.main_dialogs:_get_template_fields:1873 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-05 17:42:14.078 | INFO     | src.gui.main_dialogs:_init_field_mapping:1860 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-05 17:42:14.125 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-05 17:42:14.125 | INFO     | src.gui.main_dialogs:_apply_default_settings:2211 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-05 17:42:14.125 | INFO     | src.gui.main_dialogs:_setup_tooltips:2467 | 工具提示设置完成
2025-08-05 17:42:14.125 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2506 | 快捷键设置完成
2025-08-05 17:42:14.125 | INFO     | src.gui.main_dialogs:__init__:78 | 数据导入对话框初始化完成。
2025-08-05 17:42:14.125 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:81 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-05 17:42:14.141 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5441 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-05 17:42:14.248 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8076 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-05 17:42:21.061 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-05 17:42:26.837 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-05 17:42:26.837 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-05 17:42:26.837 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2246 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-05 17:42:28.414 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-05 17:42:28.602 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-05 17:42:28.618 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-05 17:42:28.618 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:211 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-05 17:42:28.618 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-05 17:42:28.821 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-05 17:42:28.821 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:222 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-05 17:42:28.821 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-05 17:42:28.821 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-05 17:42:28.821 | INFO     | src.utils.log_config:log_file_operation:274 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-05 17:42:28.930 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-05 17:42:28.930 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-05 17:42:28.930 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-05 17:42:28.930 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 3行 x 16列
2025-08-05 17:42:28.930 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-05 17:42:28.930 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-05 17:42:28.930 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-05 17:42:28.930 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 离休人员工资表 使用智能默认配置
2025-08-05 17:42:28.930 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-05 17:42:28.930 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 retired_employees 生成了 21 个字段映射
2025-08-05 17:42:28.930 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-08-05 17:42:28.947 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:655 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_retired_employees
2025-08-05 17:42:28.947 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:659 | 完整字段映射保存成功: salary_data_2025_08_retired_employees
2025-08-05 17:42:28.947 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_08_retired_employees 生成标准化字段映射: 21 个字段
2025-08-05 17:42:28.947 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-08-05 17:42:28.947 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-08-05 17:42:28.962 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_08_retired_employees
2025-08-05 17:42:28.962 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_08_retired_employees (模板: retired_employees)
2025-08-05 17:42:29.009 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | [FIX] [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-08-05 17:42:29.055 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-05 17:42:29.055 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | [FIX] [修复标识] 导入列名映射成功: 18 个字段已映射
2025-08-05 17:42:29.055 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。
2025-08-05 17:42:29.055 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-05 17:42:29.055 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-05 17:42:29.071 | INFO     | src.utils.log_config:log_file_operation:274 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-05 17:42:29.196 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-08-05 17:42:29.196 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-05 17:42:29.196 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-05 17:42:29.212 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 14行 x 27列
2025-08-05 17:42:29.212 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-05 17:42:29.212 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-08-05 17:42:29.212 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 13行 × 27列
2025-08-05 17:42:29.212 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 退休人员工资表 使用智能默认配置
2025-08-05 17:42:29.212 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-05 17:42:29.212 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 pension_employees 生成了 32 个字段映射
2025-08-05 17:42:29.212 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-08-05 17:42:29.227 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:655 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_pension_employees
2025-08-05 17:42:29.227 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:659 | 完整字段映射保存成功: salary_data_2025_08_pension_employees
2025-08-05 17:42:29.227 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_08_pension_employees 生成标准化字段映射: 32 个字段
2025-08-05 17:42:29.243 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-08-05 17:42:29.243 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-08-05 17:42:29.243 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_08_pension_employees
2025-08-05 17:42:29.274 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_08_pension_employees (模板: pension_employees)
2025-08-05 17:42:29.305 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | [FIX] [修复标识] 导入字段映射加载完成: 32 个映射规则
2025-08-05 17:42:29.305 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-05 17:42:29.305 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | [FIX] [修复标识] 导入列名映射成功: 29 个字段已映射
2025-08-05 17:42:29.321 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。
2025-08-05 17:42:29.321 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-05 17:42:29.321 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-05 17:42:29.321 | INFO     | src.utils.log_config:log_file_operation:274 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-05 17:42:29.477 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-05 17:42:29.477 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-05 17:42:29.493 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-05 17:42:29.493 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 1397行 x 23列
2025-08-05 17:42:29.493 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-05 17:42:29.493 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-05 17:42:29.493 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 1396行 × 23列
2025-08-05 17:42:29.493 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 全部在职人员工资表 使用智能默认配置
2025-08-05 17:42:29.493 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-08-05 17:42:29.493 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 active_employees 生成了 28 个字段映射
2025-08-05 17:42:29.493 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-08-05 17:42:29.509 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:655 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_active_employees
2025-08-05 17:42:29.509 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:659 | 完整字段映射保存成功: salary_data_2025_08_active_employees
2025-08-05 17:42:29.524 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_08_active_employees 生成标准化字段映射: 28 个字段
2025-08-05 17:42:29.524 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-08-05 17:42:29.524 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-08-05 17:42:29.524 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_08_active_employees
2025-08-05 17:42:29.539 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_08_active_employees (模板: active_employees)
2025-08-05 17:42:29.555 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | [FIX] [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-08-05 17:42:29.555 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-05 17:42:29.555 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | [FIX] [修复标识] 导入列名映射成功: 25 个字段已映射
2025-08-05 17:42:29.602 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。
2025-08-05 17:42:29.602 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-05 17:42:29.602 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-05 17:42:29.602 | INFO     | src.utils.log_config:log_file_operation:274 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-05 17:42:29.711 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-08-05 17:42:29.711 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-05 17:42:29.727 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-05 17:42:29.727 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 63行 x 21列
2025-08-05 17:42:29.727 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-05 17:42:29.727 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-08-05 17:42:29.727 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 62行 × 21列
2025-08-05 17:42:29.727 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 A岗职工 使用智能默认配置
2025-08-05 17:42:29.727 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-08-05 17:42:29.727 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-08-05 17:42:29.727 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-08-05 17:42:29.743 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:655 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_a_grade_employees
2025-08-05 17:42:29.743 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:659 | 完整字段映射保存成功: salary_data_2025_08_a_grade_employees
2025-08-05 17:42:29.743 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_08_a_grade_employees 生成标准化字段映射: 26 个字段
2025-08-05 17:42:29.743 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet A岗职工 数据处理完成: 62 行
2025-08-05 17:42:29.743 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-08-05 17:42:29.762 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_08_a_grade_employees
2025-08-05 17:42:29.774 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_08_a_grade_employees (模板: a_grade_employees)
2025-08-05 17:42:29.774 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | [FIX] [修复标识] 导入字段映射加载完成: 26 个映射规则
2025-08-05 17:42:29.774 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-05 17:42:29.774 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | [FIX] [修复标识] 导入列名映射成功: 23 个字段已映射
2025-08-05 17:42:29.790 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。
2025-08-05 17:42:29.790 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:241 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-08-05 17:42:29.790 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1480 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-08', 'data_description': '2025年8月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 08月 > 全部在职人员'}
2025-08-05 17:42:29.790 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:5460 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-08', 'data_description': '2025年8月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 08月 > 全部在职人员'}
2025-08-05 17:42:29.805 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:5471 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 08月 > 全部在职人员'
2025-08-05 17:42:29.805 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:5489 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-05 17:42:29.805 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed_immediate:5580 | 🔧 [P1-CRITICAL修复] 立即检查并更新导航面板: ['工资表', '2025年', '08月', '全部在职人员']
2025-08-05 17:42:29.805 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed_immediate:5584 | 🔧 [P1-CRITICAL修复] 检测到工资数据导入，立即刷新导航面板
2025-08-05 17:42:29.805 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed_immediate:5588 | 🔧 [P1-CRITICAL修复] 使用强制刷新方法
2025-08-05 17:42:29.805 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1602 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-08-05 17:42:29.805 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1610 | 找到工资表节点: 💰 工资表
2025-08-05 17:42:29.821 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 4 个匹配类型 'salary_data' 的表
2025-08-05 17:42:29.821 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1695 | 创建年份节点: 2025年，包含 1 个月份
2025-08-05 17:42:29.821 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1715 | 工资数据导航已强制刷新: 1 个年份, 1 个月份
2025-08-05 17:42:29.821 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1718 | 默认路径: 工资表 > 2025年 > 8月 > 全部在职人员
2025-08-05 17:42:29.821 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1758 | force_refresh_salary_data 执行完成
2025-08-05 17:42:29.821 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed_immediate:5593 | 🔧 [P0-CRITICAL修复] 将在2000ms后导航到: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-05 17:42:31.304 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8076 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-05 17:42:31.838 | INFO     | src.gui.prototype.prototype_main_window:_navigate_and_refresh_data:5613 | 🔧 [P1-CRITICAL修复] 开始导航并刷新数据: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-05 17:42:31.838 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:5710 | 尝试导航到新导入的路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-05 17:42:31.841 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:5715 | 已成功导航到新导入的路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-05 17:42:31.843 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:7289 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-05 17:42:31.843 | INFO     | src.gui.prototype.prototype_main_window:_navigate_and_refresh_data:5621 | 🔧 [P1-CRITICAL修复] 立即刷新数据显示: salary_data_2025_08_active_employees
2025-08-05 17:42:31.844 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:5811 | [数据流追踪] 开始智能数据显示刷新: salary_data_2025_08_active_employees
2025-08-05 17:42:31.851 | INFO     | src.core.smart_pagination_strategy:__init__:47 | 智能分页策略管理器初始化完成
2025-08-05 17:42:31.852 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:68 | [数据流追踪] 分页决策输入: 总记录数=1396, 页面大小=50, 用户偏好=None
2025-08-05 17:42:31.853 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:93 | [数据流追踪] 分页决策输出: 策略=pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms, 决策耗时=1ms
2025-08-05 17:42:31.855 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:5833 | [数据流追踪] 智能分页策略决策: pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms
2025-08-05 17:42:31.856 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:5907 | [数据流追踪] 执行分页显示模式: salary_data_2025_08_active_employees, 1396条记录
2025-08-05 17:42:31.857 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:6781 | 使用分页模式加载 salary_data_2025_08_active_employees，第1页，每页50条
2025-08-05 17:42:31.857 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:6839 | 🚀 [缓存修复] 使用现有table_data_service进行数据加载
2025-08-05 17:42:31.858 | INFO     | src.services.table_data_service:load_table_data:498 | 加载表格数据: salary_data_2025_08_active_employees, 页码: 1
2025-08-05 17:42:31.860 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_08_active_employees, 类型: initial_load
2025-08-05 17:42:31.862 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-05 17:42:31.863 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_08_active_employees" LIMIT 50 OFFSET 0
2025-08-05 17:42:31.876 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-08-05 17:42:31.877 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_08_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-05 17:42:31.879 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 28字段, 50行, 耗时19.3ms
2025-08-05 17:42:31.883 | INFO     | src.services.table_data_service:load_table_data:607 | [新架构] 使用统一格式管理器（单例优化）
2025-08-05 17:42:31.885 | INFO     | src.modules.format_management.format_renderer:render_dataframe:122 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'id', 'row_number', 'sequence', 'updated_at']
2025-08-05 17:42:31.885 | INFO     | src.modules.format_management.format_renderer:render_dataframe:130 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-05 17:42:31.898 | INFO     | src.modules.format_management.format_renderer:render_dataframe:148 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-05 17:42:31.900 | INFO     | src.modules.format_management.format_renderer:render_dataframe:148 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-05 17:42:31.903 | INFO     | src.modules.format_management.format_renderer:render_dataframe:148 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-05 17:42:31.908 | INFO     | src.modules.format_management.field_registry:get_display_fields:1553 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-05 17:42:31.908 | INFO     | src.modules.format_management.format_renderer:render_dataframe:168 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-05 17:42:31.909 | INFO     | src.modules.format_management.format_renderer:render_dataframe:189 | 🔧 [DEBUG] formatted_df.columns=28个, existing_display_fields=24个
2025-08-05 17:42:31.916 | INFO     | src.modules.format_management.format_renderer:render_dataframe:193 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-05 17:42:31.916 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-05 17:42:31.918 | INFO     | src.modules.format_management.unified_format_manager:format_data:412 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-05 17:42:31.918 | INFO     | src.services.table_data_service:load_table_data:619 | [统一格式化] 数据格式化完成: salary_data_2025_08_active_employees, 类型: active_employees
2025-08-05 17:42:31.920 | INFO     | src.services.table_data_service:load_table_data:690 | [修复数据发布] 数据加载成功，发布更新事件: salary_data_2025_08_active_employees, 50行
2025-08-05 17:42:31.921 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3436 | 🆕 接收到新架构数据更新事件: salary_data_2025_08_active_employees
2025-08-05 17:42:31.922 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3461 | 数据内容: 50行 x 24列
2025-08-05 17:42:31.927 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3487 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-05 17:42:31.930 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6537 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-05 17:42:31.932 | INFO     | src.gui.prototype.prototype_main_window:set_data:749 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-05 17:42:31.936 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6537 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-05 17:42:31.945 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2734 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-05 17:42:31.945 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5413 | 最大可见行数已更新: 1000 -> 50
2025-08-05 17:42:31.946 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5466 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-05 17:42:31.957 | INFO     | src.modules.format_management.format_renderer:render_dataframe:122 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'id', 'row_number', 'sequence', 'updated_at']
2025-08-05 17:42:31.958 | INFO     | src.modules.format_management.format_renderer:render_dataframe:130 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-05 17:42:31.975 | INFO     | src.modules.format_management.format_renderer:render_dataframe:148 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-05 17:42:31.976 | INFO     | src.modules.format_management.format_renderer:render_dataframe:148 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-05 17:42:31.979 | INFO     | src.modules.format_management.format_renderer:render_dataframe:148 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-05 17:42:31.980 | INFO     | src.modules.format_management.field_registry:get_display_fields:1553 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-05 17:42:31.982 | INFO     | src.modules.format_management.format_renderer:render_dataframe:168 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-05 17:42:31.992 | INFO     | src.modules.format_management.format_renderer:render_dataframe:189 | 🔧 [DEBUG] formatted_df.columns=24个, existing_display_fields=24个
2025-08-05 17:42:31.999 | INFO     | src.modules.format_management.format_renderer:render_dataframe:193 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-05 17:42:32.000 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-05 17:42:32.002 | INFO     | src.modules.format_management.unified_format_manager:format_data:412 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-05 17:42:32.036 | INFO     | src.modules.format_management.format_config:load_config:385 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-08-05 17:42:32.039 | INFO     | src.modules.format_management.format_config:save_config:435 | 🔧 [格式配置] 配置文件保存成功
2025-08-05 17:42:32.143 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-05 17:42:32.144 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-05 17:42:32.156 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时12.7ms, 平均每行0.25ms
2025-08-05 17:42:32.166 | INFO     | src.core.performance_metrics_collector:__init__:74 | 性能度量收集器初始化完成，存储路径: logs\performance_metrics.json
2025-08-05 17:42:32.168 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=12.7ms, 策略=small_dataset
2025-08-05 17:42:32.169 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-08-05 17:42:32.170 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2960 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=12.7ms, 性能评级=excellent
2025-08-05 17:42:32.171 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 19990089
2025-08-05 17:42:32.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20161565
2025-08-05 17:42:32.172 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20191782
2025-08-05 17:42:32.173 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20151515
2025-08-05 17:42:32.174 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20181640
2025-08-05 17:42:32.175 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-05 17:42:32.176 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-05 17:42:32.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-05 17:42:32.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-05 17:42:32.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-05 17:42:32.185 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-05 17:42:32.186 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-05 17:42:32.186 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-08-05 17:42:32.187 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1741 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-05 17:42:32.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed:4865 | 🔧 [P0-修复] 用户列宽设置已恢复: salary_data_2025_08_active_employees
2025-08-05 17:42:32.205 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.206 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.207 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.211 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.212 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.213 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.214 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.215 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.216 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.217 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.218 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.225 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.229 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.229 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.230 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.231 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.232 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.233 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.234 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.240 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.241 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.242 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.243 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:32.275 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_08_active_employees
2025-08-05 17:42:32.277 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_08_active_employees 重新加载 28 个字段映射
2025-08-05 17:42:32.280 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:3071 | 表格数据设置完成: 50 行, 耗时: 331.1ms
2025-08-05 17:42:32.281 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8072 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-05 17:42:32.282 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8085 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-05 17:42:32.282 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1741 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-05 17:42:32.283 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:3141 | 🔧 [P2-修复] 数据设置后已恢复列宽: salary_data_2025_08_active_employees
2025-08-05 17:42:32.286 | INFO     | src.gui.widgets.pagination_widget:set_total_records:436 | 总记录数设置为: 50
2025-08-05 17:42:32.287 | INFO     | src.gui.prototype.prototype_main_window:set_data:833 | 🔧 [P0-CRITICAL修复] 非分页上下文，设置total_records=50
2025-08-05 17:42:32.294 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3519 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-05 17:42:32.295 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:327 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-05 17:42:32.296 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3535 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-05 17:42:32.297 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3539 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-05 17:42:32.298 | INFO     | src.gui.widgets.pagination_widget:set_total_records:436 | 总记录数设置为: 1396
2025-08-05 17:42:32.299 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3562 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-08-05 17:42:32.299 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3581 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-08-05 17:42:32.300 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3587 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-08-05 17:42:32.301 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3598 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50
2025-08-05 17:42:32.302 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4212 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-05 17:42:32.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4213 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-05 17:42:32.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4214 | 🔍 [表格调试] 当前表格行数: 50
2025-08-05 17:42:32.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4215 | 🔍 [表格调试] 当前表格列数: 24
2025-08-05 17:42:32.310 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4217 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-05 17:42:32.311 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4281 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-05 17:42:32.312 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:327 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-05 17:42:32.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4381 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-05 17:42:32.313 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3600 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-05 17:42:32.315 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3614 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-05 17:42:32.318 | INFO     | src.services.table_data_service:load_table_data:711 | [修复数据发布] 数据更新事件已发布
2025-08-05 17:42:32.325 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:6919 | 缓存未命中，从数据库加载: salary_data_2025_08_active_employees 第1页
2025-08-05 17:42:32.326 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:5925 | [数据流追踪] 分页显示模式: 1396条记录分28页显示
2025-08-05 17:42:32.327 | INFO     | src.gui.prototype.prototype_main_window:run:131 | 开始加载表 salary_data_2025_08_active_employees 第1页数据，每页50条
2025-08-05 17:42:32.330 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-05 17:42:32.331 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_08_active_employees" LIMIT 50 OFFSET 0
2025-08-05 17:42:32.337 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-08-05 17:42:32.343 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_08_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-05 17:42:32.344 | INFO     | src.gui.prototype.prototype_main_window:run:157 | 使用排序查询: 0 个排序列
2025-08-05 17:42:32.348 | INFO     | src.gui.prototype.prototype_main_window:run:188 | 原始数据: 50行, 28列
2025-08-05 17:42:32.348 | INFO     | src.gui.prototype.prototype_main_window:run:195 | 开始应用字段映射
2025-08-05 17:42:32.351 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:6332 | 开始统一字段处理: salary_data_2025_08_active_employees, 原始列数: 28
2025-08-05 17:42:32.354 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-05 17:42:32.362 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-05 17:42:32.363 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:996 | 🎯 [事件驱动] 事件监听器设置完成
2025-08-05 17:42:32.370 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:1011 | 🎯 [统一状态管理] 状态同步完成
2025-08-05 17:42:32.373 | INFO     | src.modules.format_management.format_config:load_config:403 | 🔧 [格式配置] 配置文件加载成功
2025-08-05 17:42:32.379 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:736 | 🔧 [自动修复] 为表 salary_data_2025_08_retired_employees 自动生成字段类型配置
2025-08-05 17:42:32.382 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:920 | 🔧 [智能匹配] 表 salary_data_2025_08_retired_employees 匹配成功: retired_employees (得分:220, 原因:精确后缀匹配:retired_employees,英文关键词:retired,模式匹配:.*retired.*employee.*,分词匹配:retired)
2025-08-05 17:42:32.391 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:754 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_retired_employees
2025-08-05 17:42:32.392 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:736 | 🔧 [自动修复] 为表 salary_data_2025_08_pension_employees 自动生成字段类型配置
2025-08-05 17:42:32.394 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:920 | 🔧 [智能匹配] 表 salary_data_2025_08_pension_employees 匹配成功: pension_employees (得分:300, 原因:精确后缀匹配:pension_employees,英文关键词:pension,英文关键词:pension_employees,模式匹配:.*pension.*employee.*,模式匹配:.*pension.*,分词匹配:pension)
2025-08-05 17:42:32.395 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:754 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_pension_employees
2025-08-05 17:42:32.396 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:736 | 🔧 [自动修复] 为表 salary_data_2025_08_active_employees 自动生成字段类型配置
2025-08-05 17:42:32.397 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:920 | 🔧 [智能匹配] 表 salary_data_2025_08_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-05 17:42:32.398 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:754 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_active_employees
2025-08-05 17:42:32.405 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:736 | 🔧 [自动修复] 为表 salary_data_2025_08_a_grade_employees 自动生成字段类型配置
2025-08-05 17:42:32.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8107 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-05 17:42:32.435 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:920 | 🔧 [智能匹配] 表 salary_data_2025_08_a_grade_employees 匹配成功: a_grade_employees (得分:330, 原因:精确后缀匹配:a_grade_employees,英文关键词:a_grade,英文关键词:a_grade_employees,模式匹配:.*a.*grade.*employee.*,优先级加分:100)
2025-08-05 17:42:32.494 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:754 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_a_grade_employees
2025-08-05 17:42:32.495 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1741 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-05 17:42:32.496 | WARNING  | src.modules.format_management.field_registry:_validate_field_type_consistency:1908 | 🔧 [P3增强] 字段类型一致性验证失败: 'fixes'
2025-08-05 17:42:32.502 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:delayed_restore_column_widths:4257 | 🔧 [闪动修复] 分页时已强制恢复列宽设置: salary_data_2025_08_active_employees
2025-08-05 17:42:32.503 | WARNING  | src.modules.format_management.field_registry:_validate_existing_display_fields:1942 | 🔧 [P3增强] existing_display_fields验证失败: 'fixes'
2025-08-05 17:42:32.506 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:777 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'row_number', 'sequence'}", '🔧 [P1-CRITICAL修复] 表 salary_data_2025_07_active_employees existing_display_fields为空且无法自动生成，将导致FormatRenderer降级处理']
2025-08-05 17:42:32.507 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2071 | 🔧 [P1-1修复] 表 salary_data_2025_08_retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-05 17:42:32.511 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2071 | 🔧 [P1-1修复] 表 salary_data_2025_08_pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-05 17:42:32.518 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2071 | 🔧 [P1-1修复] 表 salary_data_2025_08_active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-05 17:42:32.519 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2071 | 🔧 [P1-1修复] 表 salary_data_2025_08_a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-05 17:42:32.520 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2081 | 🔧 [架构优化] 应用智能修复策略，执行 6 项修复操作
2025-08-05 17:42:32.525 | INFO     | src.modules.format_management.field_registry:save_mappings:661 | 🏷️ [字段注册] 字段映射保存成功
2025-08-05 17:42:32.537 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:785 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 4
2025-08-05 17:42:32.538 | INFO     | src.modules.format_management.field_registry:load_mappings:628 | 🏷️ [字段注册] 字段映射加载成功
2025-08-05 17:42:32.539 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:148 | 🎯 [统一格式管理] 系统初始化完成
2025-08-05 17:42:32.540 | INFO     | src.modules.format_management.unified_format_manager:__init__:132 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-08-05 17:42:32.541 | INFO     | src.core.architecture_factory:get_unified_format_manager:293 | 🎯 [统一格式管理] 统一格式管理器创建成功
2025-08-05 17:42:32.543 | INFO     | src.modules.format_management.format_renderer:render_dataframe:122 | 🎯 [格式渲染] 隐藏字段配置: ['sequence_number', 'created_at', 'id', 'row_number', 'sequence', 'updated_at']
2025-08-05 17:42:32.544 | INFO     | src.modules.format_management.format_renderer:render_dataframe:128 | 🎯 [格式渲染] 已隐藏字段: ['sequence_number', 'created_at', 'id', 'updated_at']
2025-08-05 17:42:32.563 | INFO     | src.modules.format_management.field_registry:get_display_fields:1553 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-05 17:42:32.582 | INFO     | src.modules.format_management.format_renderer:render_dataframe:168 | 🔧 [DEBUG] table_type=salary_data_2025_08_active_employees, display_fields=24个字段
2025-08-05 17:42:32.583 | INFO     | src.modules.format_management.format_renderer:render_dataframe:189 | 🔧 [DEBUG] formatted_df.columns=24个, existing_display_fields=0个
2025-08-05 17:42:32.584 | WARNING  | src.modules.format_management.format_renderer:render_dataframe:196 | 🔧 [格式修复] existing_display_fields为空，使用所有列作为降级处理
2025-08-05 17:42:32.585 | INFO     | src.modules.format_management.format_renderer:render_dataframe:198 | 🔧 [格式修复] 保持原始列顺序: 24列
2025-08-05 17:42:32.586 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎨 [格式渲染] DataFrame格式化完成: salary_data_2025_08_active_employees, 行数: 50, 列数: 24
2025-08-05 17:42:32.590 | INFO     | src.modules.format_management.unified_format_manager:format_data:412 | 🎯 [统一格式管理] 数据格式化完成: salary_data_2025_08_active_employees, 行数: 50, 列数: 24
2025-08-05 17:42:32.602 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:6340 | 🔧 [调试] 格式化时删除的列: {'sequence_number', 'id', 'created_at', 'updated_at'}
2025-08-05 17:42:32.612 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6537 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-05 17:42:32.614 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:6393 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-08-05 17:42:32.614 | INFO     | src.gui.prototype.prototype_main_window:run:205 | PaginationWorker - 字段映射成功: 28 -> 24列
2025-08-05 17:42:32.615 | INFO     | src.gui.prototype.prototype_main_window:run:219 | 字段映射成功: 24列
2025-08-05 17:42:32.617 | INFO     | src.gui.prototype.prototype_main_window:run:245 | 最终数据: 50行, 24列, 总记录数: 1396
2025-08-05 17:42:32.624 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6950 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-08-05 17:42:32.625 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6984 | 🚀 [性能缓存] 数据已缓存: salary_data_2025_08_active_employees 第1页
2025-08-05 17:42:32.636 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_08_active_employees 分页获取数据: 第2页, 每页50条
2025-08-05 17:42:32.637 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7013 | 🧹 [异步分页] 使用重构后的统一格式化结果: 50行, 24列
2025-08-05 17:42:32.638 | INFO     | src.gui.prototype.prototype_main_window:set_data:749 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-05 17:42:32.641 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6537 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-05 17:42:32.648 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2734 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-05 17:42:32.652 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_08_active_employees 获取第2页数据: 50 行，总计1396行
2025-08-05 17:42:32.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5413 | 最大可见行数已更新: 50 -> 50
2025-08-05 17:42:32.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5466 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-05 17:42:32.668 | INFO     | src.modules.format_management.format_renderer:render_dataframe:122 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'id', 'row_number', 'sequence', 'updated_at']
2025-08-05 17:42:32.668 | INFO     | src.modules.format_management.format_renderer:render_dataframe:130 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-05 17:42:32.684 | INFO     | src.modules.format_management.format_renderer:render_dataframe:148 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-05 17:42:32.685 | INFO     | src.modules.format_management.format_renderer:render_dataframe:148 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-05 17:42:32.689 | INFO     | src.modules.format_management.format_renderer:render_dataframe:148 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-05 17:42:32.691 | INFO     | src.modules.format_management.field_registry:get_display_fields:1553 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-05 17:42:32.692 | INFO     | src.modules.format_management.format_renderer:render_dataframe:168 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-05 17:42:32.693 | INFO     | src.modules.format_management.format_renderer:render_dataframe:189 | 🔧 [DEBUG] formatted_df.columns=24个, existing_display_fields=24个
2025-08-05 17:42:32.694 | INFO     | src.modules.format_management.format_renderer:render_dataframe:193 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-05 17:42:32.694 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-05 17:42:32.696 | INFO     | src.modules.format_management.unified_format_manager:format_data:412 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-05 17:42:32.726 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-05 17:42:32.727 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-05 17:42:32.740 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时12.2ms, 平均每行0.24ms
2025-08-05 17:42:32.745 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=12.2ms, 策略=small_dataset
2025-08-05 17:42:32.746 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-08-05 17:42:32.747 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2960 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=12.2ms, 性能评级=excellent
2025-08-05 17:42:32.748 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 19990089
2025-08-05 17:42:32.749 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20161565
2025-08-05 17:42:32.749 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20191782
2025-08-05 17:42:32.750 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20151515
2025-08-05 17:42:32.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20181640
2025-08-05 17:42:32.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-05 17:42:32.752 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-05 17:42:32.759 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-05 17:42:32.759 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-05 17:42:32.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-05 17:42:32.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-05 17:42:32.762 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-05 17:42:32.763 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-08-05 17:42:32.764 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed:4865 | 🔧 [P0-修复] 用户列宽设置已恢复: salary_data_2025_08_active_employees
2025-08-05 17:42:32.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:3071 | 表格数据设置完成: 50 行, 耗时: 121.8ms
2025-08-05 17:42:32.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8072 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-05 17:42:32.767 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8085 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-05 17:42:32.778 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1741 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-05 17:42:32.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:3141 | 🔧 [P2-修复] 数据设置后已恢复列宽: salary_data_2025_08_active_employees
2025-08-05 17:42:32.780 | INFO     | src.gui.widgets.pagination_widget:set_total_records:436 | 总记录数设置为: 50
2025-08-05 17:42:32.781 | INFO     | src.gui.prototype.prototype_main_window:set_data:833 | 🔧 [P0-CRITICAL修复] 非分页上下文，设置total_records=50
2025-08-05 17:42:32.782 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7032 | 🔍 [调试-分页] 即将调用set_pagination_state: 第1页, 记录1-50
2025-08-05 17:42:32.783 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7034 | 🔍 [调试-分页] set_pagination_state调用完成
2025-08-05 17:42:32.784 | INFO     | src.gui.widgets.pagination_widget:set_total_records:436 | 总记录数设置为: 1396
2025-08-05 17:42:32.792 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7056 | 🔧 [P0-新1修复] 分页状态验证: 当前第1页，共28页，总记录1396条
2025-08-05 17:42:32.796 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7107 | 🆕 [新架构] 分页数据加载完成，已完成渐进式状态迁移
2025-08-05 17:42:32.880 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8107 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-05 17:42:43.579 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: medium -> large
2025-08-05 17:42:43.580 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: large
2025-08-05 17:42:43.707 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8076 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-05 17:42:43.794 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8076 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-05 17:42:47.921 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:47.952 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:47.968 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:47.983 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.014 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.053 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.077 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.108 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.140 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.155 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.186 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.217 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.233 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.265 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.296 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.311 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.342 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.374 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.389 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.780 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.796 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.827 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:48.858 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:50.874 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:50.889 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:50.921 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:50.936 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:50.967 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.001 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.014 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.046 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.061 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.092 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.124 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.139 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.171 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.202 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.233 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.264 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.295 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.311 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.342 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.374 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.390 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.420 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.452 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.811 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:51.858 | ERROR    | src.gui.prototype.widgets.virtualized_expandable_table:_on_column_resized:1574 | 🔧 [P3-修复] 列宽调整处理失败: 'ColumnWidthManager' object has no attribute '_setup_save_timer_safe'
2025-08-05 17:42:53.030 | INFO     | src.gui.prototype.widgets.column_sort_manager:handle_header_click:132 | 🆕 [多列排序] 新增列6(2025年薪级工资)排序: 升序
2025-08-05 17:42:53.030 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7564 | 🆕 [新架构多列排序] 排序状态变化: 1 列
2025-08-05 17:42:53.045 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7572 | 🆕 [新架构多列排序] 当前排序: 2025年薪级工资: 升序
2025-08-05 17:42:53.045 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_request:7587 | 🆕 [新架构多列排序] 排序请求: salary_data_2025_08_active_employees.grade_salary_2025 -> ascending
2025-08-05 17:42:53.045 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:4133 | 🆕 [新架构排序] 处理排序应用: 列6, grade_salary_2025, ascending
2025-08-05 17:42:53.045 | INFO     | src.core.table_sort_state_manager:save_sort_state:229 | 已保存排序状态: salary_data_2025_08_active_employees (employees), 1 列
2025-08-05 17:42:53.045 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:4173 | 表头排序状态已同步到管理器
2025-08-05 17:42:53.045 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied:4180 | 排序时保持页码: 1
2025-08-05 17:42:53.045 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3844 | 🔧 [排序调试] 准备发布排序请求: salary_data_2025_08_active_employees, [{'column_name': 'grade_salary_2025', 'order': 'ascending', 'priority': 0}]
2025-08-05 17:42:53.045 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3875 | [数据流追踪] 开始处理排序请求: 表=salary_data_2025_08_active_employees, 排序列数=1, 页码=1
2025-08-05 17:42:53.045 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3923 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-08-05 17:42:53.045 | INFO     | src.services.table_data_service:_handle_sort_request:298 | [排序调试] TableDataService接收到排序请求: salary_data_2025_08_active_employees
2025-08-05 17:42:53.045 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:3962 | [数据流追踪] 排序事件已发布: 表=salary_data_2025_08_active_employees, 排序列数=1, 页码=1
2025-08-05 17:42:53.045 | INFO     | src.services.table_data_service:_handle_sort_request:299 | [排序调试] 排序列: [{'column_name': 'grade_salary_2025', 'order': 'ascending', 'priority': 0}]
2025-08-05 17:42:53.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked:8287 | 🆕 [新架构多列排序] 成功处理列6(2025年薪级工资)点击
2025-08-05 17:42:53.068 | INFO     | src.services.table_data_service:_handle_sort_request:300 | [排序调试] 当前页: 1
2025-08-05 17:42:53.072 | INFO     | src.services.table_data_service:_handle_sort_request:330 | [排序调试] 执行数据请求: DataRequest(table_name='salary_data_2025_08_active_employees', request_type=<RequestType.SORT_CHANGE: 'sort_change'>, page=1, page_size=50, sort_columns=[{'column_name': 'grade_salary_2025', 'order': 'ascending', 'priority': 0}], filters=None, selected_fields=None, preserve_page=True, preserve_sort=True, preserve_filters=True, request_id='req_1754386973.072048', timestamp=datetime.datetime(2025, 8, 5, 17, 42, 53, 72048))
2025-08-05 17:42:53.073 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_08_active_employees, 类型: sort_change
2025-08-05 17:42:53.074 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-05 17:42:53.075 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-05 17:42:53.076 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-08-05 17:42:53.081 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:516 | [排序修复] 准备排序列: grade_salary_2025
2025-08-05 17:42:53.082 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:642 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-05 17:42:53.083 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:523 | [排序修复] 列 'grade_salary_2025' 验证通过，开始排序
2025-08-05 17:42:53.084 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:532 | [FIX] [排序修复] 数值列排序: grade_salary_2025 ASC
2025-08-05 17:42:53.085 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:539 | [FIX] [排序修复] 添加排序子句: CAST("grade_salary_2025" AS REAL) ASC
2025-08-05 17:42:53.085 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_08_active_employees" ORDER BY CAST("grade_salary_2025" AS REAL) ASC LIMIT 50 OFFSET 0
2025-08-05 17:42:53.093 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19961347.0', '20251003.0', '20251006.0', '20251007.0', '20251008.0']
2025-08-05 17:42:53.096 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:566 | [FIX] [P0-CRITICAL修复] 查询结果中 grade_salary_2025 的前10个值: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 596.0]
2025-08-05 17:42:53.098 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_08_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-05 17:42:53.099 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 28字段, 50行, 耗时26.3ms
2025-08-05 17:42:53.099 | INFO     | src.services.table_data_service:_handle_sort_request:339 | [排序调试] 数据请求响应: 成功=True
2025-08-05 17:42:53.100 | INFO     | src.services.table_data_service:_handle_sort_request:343 | [排序调试] 排序数据获取成功: 50行
2025-08-05 17:42:53.101 | INFO     | src.services.table_data_service:_handle_sort_request:344 | [修复排序] 排序操作成功，发布数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-05 17:42:53.102 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3436 | 🆕 接收到新架构数据更新事件: salary_data_2025_08_active_employees
2025-08-05 17:42:53.103 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3461 | 数据内容: 50行 x 28列
2025-08-05 17:42:53.104 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3484 | 🔧 [关键修复] 接收到sort_change操作的数据更新事件，开始UI更新: salary_data_2025_08_active_employees, 50行
2025-08-05 17:42:53.111 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6537 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-05 17:42:53.113 | INFO     | src.gui.prototype.prototype_main_window:set_data:749 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-05 17:42:53.117 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6537 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-05 17:42:53.126 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2734 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20251003.0, 薪资=N/A
2025-08-05 17:42:53.127 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5413 | 最大可见行数已更新: 50 -> 50
2025-08-05 17:42:53.128 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5466 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-05 17:42:53.132 | INFO     | src.modules.format_management.format_renderer:render_dataframe:122 | 🎯 [格式渲染] 隐藏字段配置: ['created_at', 'id', 'row_number', 'sequence', 'updated_at']
2025-08-05 17:42:53.137 | INFO     | src.modules.format_management.format_renderer:render_dataframe:130 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-05 17:42:53.147 | INFO     | src.modules.format_management.format_renderer:render_dataframe:148 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-05 17:42:53.148 | INFO     | src.modules.format_management.format_renderer:render_dataframe:148 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-05 17:42:53.151 | INFO     | src.modules.format_management.format_renderer:render_dataframe:148 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-05 17:42:53.153 | INFO     | src.modules.format_management.field_registry:get_display_fields:1553 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-05 17:42:53.153 | INFO     | src.modules.format_management.format_renderer:render_dataframe:168 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-05 17:42:53.154 | INFO     | src.modules.format_management.format_renderer:render_dataframe:189 | 🔧 [DEBUG] formatted_df.columns=24个, existing_display_fields=24个
2025-08-05 17:42:53.156 | INFO     | src.modules.format_management.format_renderer:render_dataframe:193 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-05 17:42:53.160 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-05 17:42:53.161 | INFO     | src.modules.format_management.unified_format_manager:format_data:412 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-05 17:42:53.181 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-05 17:42:53.182 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-05 17:42:53.229 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时46.8ms, 平均每行0.94ms
2025-08-05 17:42:53.229 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=46.8ms, 策略=small_dataset
2025-08-05 17:42:53.230 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-08-05 17:42:53.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2960 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=46.8ms, 性能评级=excellent
2025-08-05 17:42:53.232 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:record_performance:503 | 🔧 [P1-CRITICAL修复] 渲染耗时偏高: 46.8ms (数据量: 50, 策略: small_dataset)
2025-08-05 17:42:53.233 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 19961347
2025-08-05 17:42:53.234 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20251003
2025-08-05 17:42:53.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20251006
2025-08-05 17:42:53.239 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20251007
2025-08-05 17:42:53.239 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20251008
2025-08-05 17:42:53.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-05 17:42:53.242 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=19961347, 薪资=0.00
2025-08-05 17:42:53.242 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20251003, 薪资=0.00
2025-08-05 17:42:53.243 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=20251006, 薪资=0.00
2025-08-05 17:42:53.245 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20251007, 薪资=0.00
2025-08-05 17:42:53.246 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20251008, 薪资=0.00
2025-08-05 17:42:53.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['19961347', '20251003', '20251006', '20251007', '20251008']
2025-08-05 17:42:53.255 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-08-05 17:42:53.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:3071 | 表格数据设置完成: 50 行, 耗时: 138.8ms
2025-08-05 17:42:53.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8072 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-05 17:42:53.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8082 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-05 17:42:53.263 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8083 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-08-05 17:42:53.264 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1741 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-05 17:42:53.269 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:3141 | 🔧 [P2-修复] 数据设置后已恢复列宽: salary_data_2025_08_active_employees
2025-08-05 17:42:53.275 | INFO     | src.gui.widgets.pagination_widget:set_total_records:436 | 总记录数设置为: 50
2025-08-05 17:42:53.275 | INFO     | src.gui.prototype.prototype_main_window:set_data:833 | 🔧 [P0-CRITICAL修复] 非分页上下文，设置total_records=50
2025-08-05 17:42:53.286 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3519 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-05 17:42:53.287 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:327 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-05 17:42:53.351 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3535 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-05 17:42:53.352 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3539 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-05 17:42:53.353 | INFO     | src.gui.widgets.pagination_widget:set_total_records:436 | 总记录数设置为: 1396
2025-08-05 17:42:53.354 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3562 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-08-05 17:42:53.355 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3569 | 🔧 [CRITICAL修复] 排序操作保持当前页码: 1，不进行页码重置
2025-08-05 17:42:53.356 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3581 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-08-05 17:42:53.357 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3587 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-08-05 17:42:53.357 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3598 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50
2025-08-05 17:42:53.358 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3600 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-05 17:42:53.359 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3614 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-05 17:42:53.372 | INFO     | src.services.table_data_service:_handle_sort_request:364 | [修复排序] 数据更新事件已发布
2025-08-05 17:42:53.373 | INFO     | src.services.table_data_service:_handle_sort_request:365 | [排序调试] 数据更新事件已发布
