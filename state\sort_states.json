{"salary_data_2025_08_a_grade_employees": {"table_name": "salary_data_2025_08_a_grade_employees", "table_type": "employees", "sort_columns": [{"column_name": "position_salary_2025", "order": "descending", "priority": 0}], "field_mapping": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "seniority_salary_2025": "2025年校龄工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "living_allowance_2025": "2025年生活补贴", "car_allowance": "车补", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "insurance_deduction": "保险扣款", "pension_insurance": "代扣代存养老保险", "id": "自增主键", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "is_global_sort": false, "status": "dirty", "created_at": **********.1672938, "last_updated": **********.227641, "last_accessed": **********.227641, "current_page": 2, "page_size": 50, "total_records": 0, "session_id": null}, "salary_data_2025_08_active_employees": {"table_name": "salary_data_2025_08_active_employees", "table_type": "employees", "sort_columns": [{"column_name": "position_salary_2025", "order": "descending", "priority": 0}], "field_mapping": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "employee_type": "人员类别", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "id": "自增主键", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "is_global_sort": false, "status": "dirty", "created_at": **********.5346265, "last_updated": **********.3491852, "last_accessed": **********.3491852, "current_page": 1, "page_size": 50, "total_records": 0, "session_id": null}, "salary_data_2025_08_pension_employees": {"table_name": "salary_data_2025_08_pension_employees", "table_type": "employees", "sort_columns": [{"column_name": "basic_retirement_salary", "order": "descending", "priority": 0}], "field_mapping": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "basic_retirement_salary": "基本退休费", "allowance": "津贴", "balance_allowance": "结余津贴", "retirement_living_allowance": "离退休生活补贴", "nursing_fee": "护理费", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "salary_advance": "增资预付", "adjustment_2016": "2016待遇调整", "adjustment_2017": "2017待遇调整", "adjustment_2018": "2018待遇调整", "adjustment_2019": "2019待遇调整", "adjustment_2020": "2020待遇调整", "adjustment_2021": "2021待遇调整", "adjustment_2022": "2022待遇调整", "adjustment_2023": "2023待遇调整", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund": "公积", "insurance_deduction": "保险扣款", "remarks": "备注", "id": "自增主键", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "is_global_sort": false, "status": "dirty", "created_at": 1754381902.3196151, "last_updated": 1754381906.4023166, "last_accessed": 1754381906.4023166, "current_page": 1, "page_size": 50, "total_records": 0, "session_id": null}}