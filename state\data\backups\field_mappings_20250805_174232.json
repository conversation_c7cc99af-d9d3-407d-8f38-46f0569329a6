{"version": "2.0", "last_updated": "2025-08-05T17:42:29.727520", "global_settings": {"auto_generate_mappings": true, "enable_smart_suggestions": true, "save_edit_history": true, "preserve_chinese_headers": true}, "table_mappings": {"salary_data_2025_07_active_employees": {"工号": "employee_id", "姓名": "employee_name", "部门名称": "department", "人员类别": "employee_type", "人员类别代码": "employee_type_code", "2025年岗位工资": "position_salary_2025", "2025年薪级工资": "grade_salary_2025", "津贴": "allowance", "结余津贴": "balance_allowance", "应发工资": "total_salary", "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type_code": "string", "employee_type": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "performance_bonus_2025": "float", "supplement": "float", "advance": "float", "total_salary": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "row_number": "integer"}, "hidden_fields": ["created_at", "updated_at", "id", "sequence", "row_number"], "display_order": ["employee_id", "employee_name", "department", "employee_type", "employee_type_code", "position_salary_2025", "grade_salary_2025", "allowance", "balance_allowance", "basic_performance_2025", "performance_bonus_2025", "total_salary", "health_fee", "transport_allowance", "property_allowance", "housing_allowance", "car_allowance", "communication_allowance", "provident_fund_2025", "pension_insurance", "supplement", "advance", "year", "month"], "metadata": {}}, "active_employees": {"工号": "employee_id", "姓名": "employee_name", "部门名称": "department", "人员类别": "employee_type", "人员类别代码": "employee_type_code", "display_name": "全部在职人员工资表", "field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "employee_type": "人员类别", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "provident_fund_2025": "2025公积金", "housing_allowance": "住房补贴", "car_allowance": "车补", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence": "序号", "row_number": "序号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type_code": "string", "employee_type": "string", "position_salary_2025": "float", "grade_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "health_fee": "float", "transport_allowance": "float", "property_allowance": "float", "housing_allowance": "float", "car_allowance": "float", "communication_allowance": "float", "performance_bonus_2025": "float", "supplement": "float", "advance": "float", "total_salary": "float", "provident_fund_2025": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence": "integer", "row_number": "integer"}, "hidden_fields": ["created_at", "id", "row_number", "sequence", "updated_at"], "display_order": ["employee_id", "employee_name", "department", "employee_type", "employee_type_code", "position_salary_2025", "grade_salary_2025", "allowance", "balance_allowance", "basic_performance_2025", "performance_bonus_2025", "total_salary", "health_fee", "transport_allowance", "property_allowance", "housing_allowance", "car_allowance", "communication_allowance", "provident_fund_2025", "pension_insurance", "supplement", "advance", "year", "month"], "required_fields": ["employee_id", "employee_name"], "primary_key": "employee_id", "existing_display_fields": ["工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴"], "metadata": {"existing_display_fields": ["employee_id", "employee_name", "department", "employee_type", "employee_type_code", "position_salary_2025", "grade_salary_2025", "allowance", "balance_allowance", "basic_performance_2025", "performance_bonus_2025", "total_salary", "health_fee", "transport_allowance", "property_allowance", "housing_allowance", "car_allowance", "communication_allowance", "provident_fund_2025", "pension_insurance", "supplement", "advance", "year", "month"]}}, "retired_employees": {"display_name": "离休人员工资表", "field_mappings": {"employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "basic_retirement_salary": "基本离休费", "balance_allowance": "结余津贴", "living_allowance": "生活补贴", "housing_allowance": "住房补贴", "property_allowance": "物业补贴", "retirement_allowance": "离休补贴", "nursing_fee": "护理费", "one_time_living_allowance": "增发一次性生活补贴", "supplement": "补发", "total": "合计", "advance": "借支", "remarks": "备注", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence_number": "序号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "remarks": "string", "basic_retirement_salary": "float", "balance_allowance": "float", "living_allowance": "float", "housing_allowance": "float", "property_allowance": "float", "retirement_allowance": "float", "nursing_fee": "float", "one_time_living_allowance": "float", "supplement": "float", "total": "float", "advance": "float", "year": "year_string", "month": "month_string_extract_last_two", "created_at": "date", "updated_at": "date", "id": "integer", "sequence_number": "integer"}, "hidden_fields": ["sequence_number", "created_at", "id", "updated_at"], "display_order": ["employee_id", "employee_name", "department", "basic_retirement_salary", "balance_allowance", "living_allowance", "housing_allowance", "property_allowance", "retirement_allowance", "nursing_fee", "one_time_living_allowance", "supplement", "advance", "total", "remarks", "year", "month"], "required_fields": ["employee_id", "employee_name"], "primary_key": "employee_id", "metadata": {"existing_display_fields": ["employee_id", "employee_name", "department", "basic_retirement_salary", "balance_allowance", "living_allowance", "housing_allowance", "property_allowance", "retirement_allowance", "nursing_fee", "one_time_living_allowance", "supplement", "advance", "total", "remarks", "year", "month"]}}, "pension_employees": {"display_name": "退休人员工资表", "field_mappings": {"employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "basic_retirement_salary": "基本退休费", "allowance": "津贴", "balance_allowance": "结余津贴", "retirement_living_allowance": "离退休生活补贴", "nursing_fee": "护理费", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "salary_advance": "增资预付", "adjustment_2016": "2016待遇调整", "adjustment_2017": "2017待遇调整", "adjustment_2018": "2018待遇调整", "adjustment_2019": "2019待遇调整", "adjustment_2020": "2020待遇调整", "adjustment_2021": "2021待遇调整", "adjustment_2022": "2022待遇调整", "adjustment_2023": "2023待遇调整", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund": "公积", "insurance_deduction": "保险扣款", "remarks": "备注", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence_number": "序号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type_code": "string", "remarks": "string", "basic_retirement_salary": "float", "allowance": "float", "balance_allowance": "float", "retirement_living_allowance": "float", "nursing_fee": "float", "property_allowance": "float", "housing_allowance": "float", "salary_advance": "float", "adjustment_2016": "float", "adjustment_2017": "float", "adjustment_2018": "float", "adjustment_2019": "float", "adjustment_2020": "float", "adjustment_2021": "float", "adjustment_2022": "float", "adjustment_2023": "float", "supplement": "float", "advance": "float", "total_salary": "float", "provident_fund": "float", "insurance_deduction": "float", "year": "year_string", "month": "month_string_extract_last_two", "created_at": "date", "updated_at": "date", "id": "integer", "sequence_number": "integer"}, "hidden_fields": ["sequence_number", "created_at", "id", "updated_at"], "display_order": ["employee_id", "employee_name", "department", "employee_type_code", "basic_retirement_salary", "allowance", "balance_allowance", "retirement_living_allowance", "nursing_fee", "property_allowance", "housing_allowance", "salary_advance", "adjustment_2016", "adjustment_2017", "adjustment_2018", "adjustment_2019", "adjustment_2020", "adjustment_2021", "adjustment_2022", "adjustment_2023", "supplement", "advance", "total_salary", "provident_fund", "insurance_deduction", "remarks", "year", "month"], "required_fields": ["employee_id", "employee_name"], "primary_key": "employee_id", "metadata": {"existing_display_fields": ["employee_id", "employee_name", "department", "employee_type_code", "basic_retirement_salary", "allowance", "balance_allowance", "retirement_living_allowance", "nursing_fee", "property_allowance", "housing_allowance", "salary_advance", "adjustment_2016", "adjustment_2017", "adjustment_2018", "adjustment_2019", "adjustment_2020", "adjustment_2021", "adjustment_2022", "adjustment_2023", "supplement", "advance", "total_salary", "provident_fund", "insurance_deduction", "remarks", "year", "month"]}}, "part_time_employees": {"display_name": "临时工工资表", "field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "hourly_rate": "小时工资", "hours_worked": "工作小时", "total_salary": "应发工资", "year": "年份", "month": "月份"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "hourly_rate": "currency", "hours_worked": "float", "total_salary": "float", "year": "integer", "month": "integer"}, "required_fields": ["employee_id", "employee_name"], "primary_key": "employee_id", "metadata": {"existing_display_fields": ["employee_id", "employee_name", "department", "hourly_rate", "hours_worked", "total_salary", "year", "month"]}}, "contract_employees": {"display_name": "合同工工资表", "field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "base_salary": "基本工资", "performance_bonus": "绩效奖金", "allowance": "津贴", "total_salary": "应发工资", "social_insurance": "社会保险", "year": "年份", "month": "月份"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "base_salary": "currency", "performance_bonus": "currency", "allowance": "currency", "total_salary": "currency", "social_insurance": "currency", "year": "integer", "month": "integer"}, "required_fields": ["employee_id", "employee_name"], "primary_key": "employee_id", "metadata": {"existing_display_fields": ["employee_id", "employee_name", "department", "base_salary", "performance_bonus", "allowance", "total_salary", "social_insurance", "year", "month"]}}, "a_grade_employees": {"display_name": "A岗职工工资表", "field_mappings": {"employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "seniority_salary_2025": "2025年校龄工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "living_allowance_2025": "2025年生活补贴", "car_allowance": "车补", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "insurance_deduction": "保险扣款", "pension_insurance": "代扣代存养老保险", "year": "年份", "month": "月份", "created_at": "创建时间", "updated_at": "更新时间", "id": "自增主键", "sequence_number": "序号"}, "field_types": {"employee_id": "string", "employee_name": "string", "department": "string", "employee_type": "string", "employee_type_code": "string", "position_salary_2025": "float", "seniority_salary_2025": "float", "allowance": "float", "balance_allowance": "float", "basic_performance_2025": "float", "health_fee": "float", "living_allowance_2025": "float", "car_allowance": "float", "performance_bonus_2025": "float", "supplement": "float", "advance": "float", "total_salary": "float", "provident_fund_2025": "float", "insurance_deduction": "float", "pension_insurance": "float", "year": "year_string", "month": "month_string", "created_at": "date", "updated_at": "date", "id": "integer", "sequence_number": "integer"}, "hidden_fields": ["sequence_number", "created_at", "id", "updated_at"], "display_order": ["employee_id", "employee_name", "department", "employee_type", "employee_type_code", "position_salary_2025", "seniority_salary_2025", "allowance", "balance_allowance", "basic_performance_2025", "health_fee", "living_allowance_2025", "car_allowance", "performance_bonus_2025", "supplement", "advance", "total_salary", "provident_fund_2025", "insurance_deduction", "pension_insurance", "year", "month"], "required_fields": ["employee_id", "employee_name"], "primary_key": "employee_id", "metadata": {"existing_display_fields": ["employee_id", "employee_name", "department", "employee_type", "employee_type_code", "position_salary_2025", "seniority_salary_2025", "allowance", "balance_allowance", "basic_performance_2025", "health_fee", "living_allowance_2025", "car_allowance", "performance_bonus_2025", "supplement", "advance", "total_salary", "provident_fund_2025", "insurance_deduction", "pension_insurance", "year", "month"]}}, "salary_data_2025_08_retired_employees": {"field_mappings": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "basic_retirement_salary": "基本离休费", "balance_allowance": "结余津贴", "living_allowance": "生活补贴", "housing_allowance": "住房补贴", "property_allowance": "物业补贴", "retirement_allowance": "离休补贴", "nursing_fee": "护理费", "one_time_living_allowance": "增发一次性生活补贴", "supplement": "补发", "total": "合计", "advance": "借支", "remarks": "备注", "id": "自增主键", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "original_excel_headers": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "basic_retirement_salary": "基本\n离休费", "balance_allowance": "结余\n津贴", "living_allowance": "生活\n补贴", "housing_allowance": "住房\n补贴", "property_allowance": "物业\n补贴", "retirement_allowance": "离休\n补贴", "nursing_fee": "护理费", "one_time_living_allowance": "增发一次\n性生活补贴", "supplement": "补发", "total": "合计", "advance": "借支", "remarks": "备注"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-08-05T17:42:28.947873", "sheet_name": "离休人员工资表", "has_chinese_headers": true}}, "salary_data_2025_08_pension_employees": {"field_mappings": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "basic_retirement_salary": "基本退休费", "allowance": "津贴", "balance_allowance": "结余津贴", "retirement_living_allowance": "离退休生活补贴", "nursing_fee": "护理费", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "salary_advance": "增资预付", "adjustment_2016": "2016待遇调整", "adjustment_2017": "2017待遇调整", "adjustment_2018": "2018待遇调整", "adjustment_2019": "2019待遇调整", "adjustment_2020": "2020待遇调整", "adjustment_2021": "2021待遇调整", "adjustment_2022": "2022待遇调整", "adjustment_2023": "2023待遇调整", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund": "公积", "insurance_deduction": "保险扣款", "remarks": "备注", "id": "自增主键", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "original_excel_headers": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "basic_retirement_salary": "基本退休费", "allowance": "津贴", "balance_allowance": "结余津贴", "retirement_living_allowance": "离退休生活补贴", "nursing_fee": "护理费", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "salary_advance": "增资预付", "adjustment_2016": "2016待遇调整", "adjustment_2017": "2017待遇调整", "adjustment_2018": "2018待遇调整", "adjustment_2019": "2019待遇调整", "adjustment_2020": "2020待遇调整", "adjustment_2021": "2021待遇调整", "adjustment_2022": "2022待遇调整", "adjustment_2023": "2023待遇调整", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund": "公积", "insurance_deduction": "保险扣款", "remarks": "备注"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-08-05T17:42:29.212571", "sheet_name": "退休人员工资表", "has_chinese_headers": true}}, "salary_data_2025_08_active_employees": {"field_mappings": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "employee_type": "人员类别", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险", "id": "自增主键", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "original_excel_headers": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "employee_type": "人员类别", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-08-05T17:42:29.509054", "sheet_name": "全部在职人员工资表", "has_chinese_headers": true}}, "salary_data_2025_08_a_grade_employees": {"field_mappings": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "seniority_salary_2025": "2025年校龄工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "living_allowance_2025": "2025年生活补贴", "car_allowance": "车补", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "insurance_deduction": "保险扣款", "pension_insurance": "代扣代存养老保险", "id": "自增主键", "month": "月份", "year": "年份", "created_at": "创建时间", "updated_at": "更新时间"}, "original_excel_headers": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "seniority_salary_2025": "2025年校龄工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "living_allowance_2025": "2025年生活补贴", "car_allowance": "车补", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "insurance_deduction": "保险扣款", "pension_insurance": "代扣代存养老保险"}, "metadata": {"source": "excel_import", "auto_generated": true, "user_modified": false, "created_at": "2025-08-05T17:42:29.727520", "sheet_name": "A岗职工", "has_chinese_headers": true}}}, "field_templates": {"离休人员工资表": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "basic_retirement_salary": "基本离休费", "balance_allowance": "结余津贴", "living_allowance": "生活补贴", "housing_allowance": "住房补贴", "property_allowance": "物业补贴", "retirement_allowance": "离休补贴", "nursing_fee": "护理费", "one_time_living_allowance": "增发一次性生活补贴", "supplement": "补发", "total": "合计", "advance": "借支", "remarks": "备注"}, "退休人员工资表": {"sequence_number": "序号", "employee_id": "人员代码", "employee_name": "姓名", "department": "部门名称", "employee_type_code": "人员类别代码", "basic_retirement_salary": "基本退休费", "allowance": "津贴", "balance_allowance": "结余津贴", "retirement_living_allowance": "离退休生活补贴", "nursing_fee": "护理费", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "salary_advance": "增资预付", "adjustment_2016": "2016待遇调整", "adjustment_2017": "2017待遇调整", "adjustment_2018": "2018待遇调整", "adjustment_2019": "2019待遇调整", "adjustment_2020": "2020待遇调整", "adjustment_2021": "2021待遇调整", "adjustment_2022": "2022待遇调整", "adjustment_2023": "2023待遇调整", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund": "公积", "insurance_deduction": "保险扣款", "remarks": "备注"}, "全部在职人员工资表": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "grade_salary_2025": "2025年薪级工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "transport_allowance": "交通补贴", "property_allowance": "物业补贴", "housing_allowance": "住房补贴", "car_allowance": "车补", "communication_allowance": "通讯补贴", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "pension_insurance": "代扣代存养老保险"}, "A岗职工": {"sequence_number": "序号", "employee_id": "工号", "employee_name": "姓名", "department": "部门名称", "employee_type": "人员类别", "employee_type_code": "人员类别代码", "position_salary_2025": "2025年岗位工资", "seniority_salary_2025": "2025年校龄工资", "allowance": "津贴", "balance_allowance": "结余津贴", "basic_performance_2025": "2025年基础性绩效", "health_fee": "卫生费", "living_allowance_2025": "2025年生活补贴", "car_allowance": "车补", "performance_bonus_2025": "2025年奖励性绩效预发", "supplement": "补发", "advance": "借支", "total_salary": "应发工资", "provident_fund_2025": "2025公积金", "insurance_deduction": "保险扣款", "pension_insurance": "代扣代存养老保险"}}, "user_preferences": {"default_field_patterns": {}, "recent_edits": [], "favorite_mappings": []}, "metadata": {"version": "1.0.0", "created_at": "2025-08-05T17:42:06.731298", "description": "字段映射配置文件", "last_updated": "2025-08-05T17:42:06.762672", "last_modified": "2025-08-05T17:42:06.762672"}, "global_aliases": {"id": "employee_id", "name": "employee_name", "dept": "department", "salary": "total_salary", "工资": "total_salary", "工号": "employee_id", "姓名": "employee_name", "部门": "department"}, "field_patterns": {"salary_patterns": [".*salary.*", ".*工资.*", ".*薪.*"], "allowance_patterns": [".*allowance.*", ".*津贴.*", ".*补贴.*"], "bonus_patterns": [".*bonus.*", ".*奖金.*", ".*绩效.*"]}}